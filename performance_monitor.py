#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票扫描器性能监控工具
监控扫描性能、数据质量和系统资源使用情况
"""

import sqlite3
import pandas as pd
import time
import psutil
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import json

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, db_path="stock_data.db"):
        self.db_path = db_path
        self.start_time = None
        self.metrics = {}
    
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.metrics = {
            'start_time': datetime.now(),
            'cpu_usage': [],
            'memory_usage': [],
            'disk_usage': [],
            'network_io': [],
            'scan_progress': []
        }
        print("📊 性能监控已启动")
    
    def record_system_metrics(self):
        """记录系统指标"""
        if not self.start_time:
            return
        
        current_time = time.time() - self.start_time
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        self.metrics['cpu_usage'].append((current_time, cpu_percent))
        
        # 内存使用率
        memory = psutil.virtual_memory()
        self.metrics['memory_usage'].append((current_time, memory.percent))
        
        # 磁盘使用率
        disk = psutil.disk_usage('.')
        self.metrics['disk_usage'].append((current_time, disk.percent))
        
        # 网络IO
        net_io = psutil.net_io_counters()
        self.metrics['network_io'].append((current_time, net_io.bytes_sent + net_io.bytes_recv))
    
    def analyze_scan_performance(self, scan_id: str = None) -> Dict:
        """分析扫描性能"""
        conn = sqlite3.connect(self.db_path)
        
        # 获取扫描进度信息
        if scan_id:
            progress_query = "SELECT * FROM scan_progress WHERE scan_id = ?"
            params = (scan_id,)
        else:
            progress_query = "SELECT * FROM scan_progress ORDER BY start_time DESC LIMIT 1"
            params = ()
        
        progress_df = pd.read_sql_query(progress_query, conn, params=params)
        
        if progress_df.empty:
            conn.close()
            return {}
        
        progress = progress_df.iloc[0]
        
        # 计算性能指标
        start_time = pd.to_datetime(progress['start_time'])
        end_time = pd.to_datetime(progress['end_time']) if progress['end_time'] else datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 处理速度
        processing_speed = progress['processed_stocks'] / max(duration, 1) * 60  # 股票/分钟
        
        # 成功率
        success_rate = progress['successful_stocks'] / max(progress['processed_stocks'], 1) * 100
        
        # 完成率
        completion_rate = progress['processed_stocks'] / max(progress['total_stocks'], 1) * 100
        
        # 获取错误统计
        error_query = "SELECT COUNT(*) as error_count FROM scan_errors WHERE scan_id = ?"
        error_df = pd.read_sql_query(error_query, conn, params=(progress['scan_id'],))
        error_count = error_df.iloc[0]['error_count']
        
        # 获取结果统计
        result_query = "SELECT COUNT(*) as result_count FROM scan_results WHERE scan_id = ?"
        result_df = pd.read_sql_query(result_query, conn, params=(progress['scan_id'],))
        result_count = result_df.iloc[0]['result_count']
        
        conn.close()
        
        return {
            'scan_id': progress['scan_id'],
            'duration_seconds': duration,
            'duration_minutes': duration / 60,
            'processing_speed': processing_speed,
            'success_rate': success_rate,
            'completion_rate': completion_rate,
            'total_stocks': progress['total_stocks'],
            'processed_stocks': progress['processed_stocks'],
            'successful_stocks': progress['successful_stocks'],
            'failed_stocks': progress['failed_stocks'],
            'error_count': error_count,
            'result_count': result_count,
            'efficiency_score': self._calculate_efficiency_score(processing_speed, success_rate, completion_rate)
        }
    
    def _calculate_efficiency_score(self, speed: float, success_rate: float, completion_rate: float) -> float:
        """计算效率评分"""
        # 归一化各项指标
        speed_score = min(speed / 100, 1.0)  # 假设100股票/分钟为满分
        success_score = success_rate / 100
        completion_score = completion_rate / 100
        
        # 加权平均
        efficiency = (speed_score * 0.4 + success_score * 0.4 + completion_score * 0.2) * 100
        return round(efficiency, 2)
    
    def analyze_data_quality(self) -> Dict:
        """分析数据质量"""
        conn = sqlite3.connect(self.db_path)
        
        # 数据质量统计
        quality_query = """
            SELECT 
                COUNT(*) as total_stocks,
                AVG(data_quality_score) as avg_quality,
                COUNT(CASE WHEN data_quality_score >= 0.8 THEN 1 END) as high_quality,
                COUNT(CASE WHEN data_quality_score >= 0.5 THEN 1 END) as medium_quality,
                COUNT(CASE WHEN data_quality_score < 0.5 THEN 1 END) as low_quality,
                MAX(last_update_date) as latest_update,
                MIN(last_update_date) as oldest_update
            FROM data_update_status
        """
        
        quality_df = pd.read_sql_query(quality_query, conn)
        quality_stats = quality_df.iloc[0].to_dict()
        
        # 数据覆盖率
        coverage_query = """
            SELECT 
                COUNT(DISTINCT code) as stocks_with_data,
                COUNT(*) as total_records,
                AVG(CASE WHEN date >= date('now', '-7 days') THEN 1 ELSE 0 END) as recent_data_ratio
            FROM daily_data
        """
        
        coverage_df = pd.read_sql_query(coverage_query, conn)
        coverage_stats = coverage_df.iloc[0].to_dict()
        
        # 技术指标覆盖率
        indicator_query = """
            SELECT 
                COUNT(DISTINCT code) as stocks_with_indicators,
                COUNT(*) as total_indicator_records
            FROM technical_indicators
        """
        
        indicator_df = pd.read_sql_query(indicator_query, conn)
        indicator_stats = indicator_df.iloc[0].to_dict()
        
        conn.close()
        
        return {
            'quality_stats': quality_stats,
            'coverage_stats': coverage_stats,
            'indicator_stats': indicator_stats,
            'data_health_score': self._calculate_data_health_score(quality_stats, coverage_stats, indicator_stats)
        }
    
    def _calculate_data_health_score(self, quality_stats: Dict, coverage_stats: Dict, indicator_stats: Dict) -> float:
        """计算数据健康评分"""
        # 质量评分
        quality_score = quality_stats.get('avg_quality', 0) * 100
        
        # 覆盖率评分
        total_stocks = quality_stats.get('total_stocks', 1)
        coverage_score = (coverage_stats.get('stocks_with_data', 0) / total_stocks) * 100
        
        # 指标覆盖率评分
        indicator_score = (indicator_stats.get('stocks_with_indicators', 0) / total_stocks) * 100
        
        # 综合评分
        health_score = (quality_score * 0.4 + coverage_score * 0.3 + indicator_score * 0.3)
        return round(health_score, 2)
    
    def generate_performance_report(self, scan_id: str = None) -> str:
        """生成性能报告"""
        print("📊 生成性能报告...")
        
        # 分析扫描性能
        scan_perf = self.analyze_scan_performance(scan_id)
        
        # 分析数据质量
        data_quality = self.analyze_data_quality()
        
        # 生成报告
        report = []
        report.append("=" * 60)
        report.append("📊 股票扫描器性能报告")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if scan_perf:
            report.append(f"\n🔍 扫描性能分析")
            report.append(f"扫描ID: {scan_perf['scan_id'][:8]}...")
            report.append(f"总耗时: {scan_perf['duration_minutes']:.1f} 分钟")
            report.append(f"处理速度: {scan_perf['processing_speed']:.1f} 股票/分钟")
            report.append(f"成功率: {scan_perf['success_rate']:.1f}%")
            report.append(f"完成率: {scan_perf['completion_rate']:.1f}%")
            report.append(f"效率评分: {scan_perf['efficiency_score']}/100")
            report.append(f"")
            report.append(f"📈 处理统计:")
            report.append(f"  总股票数: {scan_perf['total_stocks']:,}")
            report.append(f"  已处理: {scan_perf['processed_stocks']:,}")
            report.append(f"  成功筛选: {scan_perf['successful_stocks']:,}")
            report.append(f"  失败数量: {scan_perf['failed_stocks']:,}")
            report.append(f"  符合条件: {scan_perf['result_count']:,}")
        
        if data_quality:
            quality_stats = data_quality['quality_stats']
            coverage_stats = data_quality['coverage_stats']
            indicator_stats = data_quality['indicator_stats']
            
            report.append(f"\n💾 数据质量分析")
            report.append(f"数据健康评分: {data_quality['data_health_score']}/100")
            report.append(f"")
            report.append(f"📊 质量统计:")
            report.append(f"  总股票数: {quality_stats['total_stocks']:,}")
            report.append(f"  平均质量: {quality_stats['avg_quality']:.2f}")
            report.append(f"  高质量股票: {quality_stats['high_quality']:,}")
            report.append(f"  中等质量股票: {quality_stats['medium_quality']:,}")
            report.append(f"  低质量股票: {quality_stats['low_quality']:,}")
            report.append(f"")
            report.append(f"📈 数据覆盖:")
            report.append(f"  有数据股票: {coverage_stats['stocks_with_data']:,}")
            report.append(f"  总数据记录: {coverage_stats['total_records']:,}")
            report.append(f"  技术指标股票: {indicator_stats['stocks_with_indicators']:,}")
        
        # 性能建议
        report.append(f"\n💡 性能优化建议:")
        
        if scan_perf:
            if scan_perf['processing_speed'] < 50:
                report.append("  - 处理速度较慢，建议增加并发线程数")
            if scan_perf['success_rate'] < 90:
                report.append("  - 成功率较低，建议检查网络连接和API限制")
            if scan_perf['efficiency_score'] < 70:
                report.append("  - 整体效率偏低，建议优化配置参数")
        
        if data_quality and data_quality['data_health_score'] < 80:
            report.append("  - 数据质量需要改善，建议重新收集数据")
        
        report.append(f"\n" + "=" * 60)
        
        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"performance_report_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        # 打印报告
        print('\n'.join(report))
        print(f"\n📄 报告已保存到: {filename}")
        
        return filename
    
    def plot_performance_trends(self, days: int = 7):
        """绘制性能趋势图"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            
            conn = sqlite3.connect(self.db_path)
            
            # 获取最近的扫描数据
            cutoff_date = datetime.now() - timedelta(days=days)
            
            query = """
                SELECT 
                    sp.start_time,
                    sp.total_stocks,
                    sp.processed_stocks,
                    sp.successful_stocks,
                    (sp.processed_stocks * 1.0 / sp.total_stocks * 100) as completion_rate,
                    (sp.successful_stocks * 1.0 / sp.processed_stocks * 100) as success_rate,
                    COUNT(sr.id) as result_count
                FROM scan_progress sp
                LEFT JOIN scan_results sr ON sp.scan_id = sr.scan_id
                WHERE sp.start_time >= ?
                GROUP BY sp.scan_id
                ORDER BY sp.start_time
            """
            
            df = pd.read_sql_query(query, conn, params=(cutoff_date,))
            conn.close()
            
            if df.empty:
                print("📊 没有足够的数据绘制趋势图")
                return
            
            df['start_time'] = pd.to_datetime(df['start_time'])
            
            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('股票扫描器性能趋势', fontsize=16)
            
            # 完成率趋势
            ax1.plot(df['start_time'], df['completion_rate'], 'b-o')
            ax1.set_title('完成率趋势')
            ax1.set_ylabel('完成率 (%)')
            ax1.grid(True)
            
            # 成功率趋势
            ax2.plot(df['start_time'], df['success_rate'], 'g-o')
            ax2.set_title('成功率趋势')
            ax2.set_ylabel('成功率 (%)')
            ax2.grid(True)
            
            # 处理股票数趋势
            ax3.plot(df['start_time'], df['processed_stocks'], 'r-o')
            ax3.set_title('处理股票数趋势')
            ax3.set_ylabel('股票数')
            ax3.grid(True)
            
            # 结果数量趋势
            ax4.plot(df['start_time'], df['result_count'], 'm-o')
            ax4.set_title('筛选结果数趋势')
            ax4.set_ylabel('结果数量')
            ax4.grid(True)
            
            # 格式化x轴
            for ax in [ax1, ax2, ax3, ax4]:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
                ax.tick_params(axis='x', rotation=45)
            
            plt.tight_layout()
            
            # 保存图表
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"performance_trends_{timestamp}.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            
            print(f"📈 性能趋势图已保存到: {filename}")
            plt.show()
            
        except ImportError:
            print("⚠️  需要安装matplotlib库来绘制图表: pip install matplotlib")
        except Exception as e:
            print(f"❌ 绘制趋势图失败: {e}")

def main():
    """主函数"""
    monitor = PerformanceMonitor()
    
    print("📊 股票扫描器性能监控工具")
    print("1. 生成性能报告")
    print("2. 分析数据质量")
    print("3. 绘制性能趋势")
    print("4. 实时监控")
    
    choice = input("\n请选择功能 (1-4): ").strip()
    
    if choice == "1":
        scan_id = input("输入扫描ID (回车分析最新扫描): ").strip() or None
        monitor.generate_performance_report(scan_id)
    
    elif choice == "2":
        data_quality = monitor.analyze_data_quality()
        print(f"\n💾 数据质量分析结果:")
        print(f"数据健康评分: {data_quality['data_health_score']}/100")
        print(json.dumps(data_quality, indent=2, ensure_ascii=False, default=str))
    
    elif choice == "3":
        days = int(input("分析最近多少天的数据 (默认7天): ") or "7")
        monitor.plot_performance_trends(days)
    
    elif choice == "4":
        print("🔄 启动实时监控 (Ctrl+C 停止)")
        monitor.start_monitoring()
        
        try:
            while True:
                monitor.record_system_metrics()
                time.sleep(10)  # 每10秒记录一次
        except KeyboardInterrupt:
            print("\n⏹️  监控已停止")
    
    else:
        print("❌ 无效的选择")

if __name__ == "__main__":
    main()
