#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票扫描器配置文件
用于优化性能参数和避免API限制
"""

import os
from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class PerformanceConfig:
    """性能配置"""
    # 并发设置
    max_workers: int = 3  # 最大工作线程数
    batch_size: int = 50  # 批处理大小
    
    # 请求频率控制
    request_delay: float = 0.1  # 请求间隔（秒）
    batch_delay: float = 2.0   # 批次间隔（秒）
    
    # 重试设置
    max_retries: int = 3       # 最大重试次数
    retry_delay: float = 1.0   # 重试间隔（秒）
    
    # 超时设置
    request_timeout: int = 30  # 请求超时（秒）
    
    # 数据质量
    min_data_quality: float = 0.5  # 最小数据质量分数
    min_data_days: int = 20        # 最少数据天数

@dataclass
class DatabaseConfig:
    """数据库配置"""
    db_path: str = "stock_data.db"
    backup_enabled: bool = True
    backup_interval_days: int = 7
    cleanup_days: int = 30
    
    # 连接池设置
    connection_timeout: int = 30
    max_connections: int = 10

@dataclass
class DataConfig:
    """数据配置"""
    # 历史数据天数
    default_history_days: int = 60
    max_history_days: int = 252  # 一年交易日
    
    # 数据更新策略
    update_threshold_days: int = 1  # 数据过期阈值
    force_update_days: int = 7      # 强制更新间隔
    
    # 股票过滤
    exclude_st_stocks: bool = True   # 排除ST股票
    exclude_new_stocks: bool = True  # 排除新股（上市不足30天）
    min_market_cap: float = 0        # 最小市值（亿元）

@dataclass
class StrategyConfig:
    """策略配置"""
    # MACDBollVolumeStrategy参数
    macd_fast: int = 12
    macd_slow: int = 26
    macd_signal: int = 9
    
    boll_period: int = 20
    boll_std: float = 2.0
    
    volume_ma_period: int = 20
    volume_ratio_threshold: float = 2.0
    
    # 策略条件权重
    golden_cross_weight: float = 1.0
    big_yang_volume_weight: float = 1.0
    macd_uptrend_weight: float = 0.8
    price_range_weight: float = 0.6
    
    # 筛选阈值
    min_score: int = 3
    max_results: int = 100

@dataclass
class LoggingConfig:
    """日志配置"""
    log_level: str = "INFO"
    log_file: str = "stock_screener.log"
    max_log_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    
    # 控制台输出
    console_output: bool = True
    progress_update_interval: int = 50  # 每处理多少只股票显示进度

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file
        self.performance = PerformanceConfig()
        self.database = DatabaseConfig()
        self.data = DataConfig()
        self.strategy = StrategyConfig()
        self.logging = LoggingConfig()
        
        # 根据系统性能自动调整配置
        self._auto_tune_performance()
        
        # 加载自定义配置
        if config_file and os.path.exists(config_file):
            self._load_config(config_file)
    
    def _auto_tune_performance(self):
        """根据系统性能自动调整配置"""
        import psutil
        
        # 根据CPU核心数调整并发
        cpu_count = psutil.cpu_count()
        if cpu_count >= 8:
            self.performance.max_workers = 5
            self.performance.batch_size = 100
        elif cpu_count >= 4:
            self.performance.max_workers = 3
            self.performance.batch_size = 50
        else:
            self.performance.max_workers = 2
            self.performance.batch_size = 25
        
        # 根据内存大小调整批处理
        memory_gb = psutil.virtual_memory().total / (1024**3)
        if memory_gb < 4:
            self.performance.batch_size = min(self.performance.batch_size, 25)
        elif memory_gb >= 16:
            self.performance.batch_size = min(self.performance.batch_size * 2, 200)
        
        print(f"🔧 自动调优: CPU核心={cpu_count}, 内存={memory_gb:.1f}GB")
        print(f"🔧 配置: 工作线程={self.performance.max_workers}, 批大小={self.performance.batch_size}")
    
    def _load_config(self, config_file: str):
        """加载配置文件"""
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 更新配置
            for section, values in config_data.items():
                if hasattr(self, section):
                    section_obj = getattr(self, section)
                    for key, value in values.items():
                        if hasattr(section_obj, key):
                            setattr(section_obj, key, value)
            
            print(f"✅ 配置文件加载成功: {config_file}")
        except Exception as e:
            print(f"⚠️  配置文件加载失败: {e}")
    
    def save_config(self, config_file: str = None):
        """保存配置到文件"""
        if config_file is None:
            config_file = self.config_file or "config.json"
        
        config_data = {
            'performance': self.performance.__dict__,
            'database': self.database.__dict__,
            'data': self.data.__dict__,
            'strategy': self.strategy.__dict__,
            'logging': self.logging.__dict__
        }
        
        try:
            import json
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            print(f"✅ 配置已保存到: {config_file}")
        except Exception as e:
            print(f"❌ 配置保存失败: {e}")
    
    def get_optimized_config(self, mode: str = "balanced") -> Dict[str, Any]:
        """获取优化配置"""
        configs = {
            "fast": {
                "max_workers": min(self.performance.max_workers * 2, 8),
                "batch_size": min(self.performance.batch_size * 2, 200),
                "request_delay": 0.05,
                "batch_delay": 1.0,
                "min_data_quality": 0.3
            },
            "balanced": {
                "max_workers": self.performance.max_workers,
                "batch_size": self.performance.batch_size,
                "request_delay": self.performance.request_delay,
                "batch_delay": self.performance.batch_delay,
                "min_data_quality": self.performance.min_data_quality
            },
            "stable": {
                "max_workers": max(self.performance.max_workers // 2, 1),
                "batch_size": max(self.performance.batch_size // 2, 10),
                "request_delay": self.performance.request_delay * 2,
                "batch_delay": self.performance.batch_delay * 2,
                "min_data_quality": 0.8
            }
        }
        
        return configs.get(mode, configs["balanced"])
    
    def print_config(self):
        """打印当前配置"""
        print("\n📋 当前配置:")
        print(f"🔧 性能配置:")
        print(f"   - 工作线程: {self.performance.max_workers}")
        print(f"   - 批处理大小: {self.performance.batch_size}")
        print(f"   - 请求间隔: {self.performance.request_delay}s")
        print(f"   - 批次间隔: {self.performance.batch_delay}s")
        
        print(f"💾 数据库配置:")
        print(f"   - 数据库路径: {self.database.db_path}")
        print(f"   - 清理周期: {self.database.cleanup_days}天")
        
        print(f"📊 数据配置:")
        print(f"   - 历史数据: {self.data.default_history_days}天")
        print(f"   - 排除ST股票: {self.data.exclude_st_stocks}")
        print(f"   - 最小数据质量: {self.performance.min_data_quality}")

# 全局配置实例
config = ConfigManager()

# 预设配置模板
PRESET_CONFIGS = {
    "development": {
        "performance": {
            "max_workers": 2,
            "batch_size": 20,
            "request_delay": 0.2
        },
        "data": {
            "default_history_days": 30
        }
    },
    "production": {
        "performance": {
            "max_workers": 5,
            "batch_size": 100,
            "request_delay": 0.05
        },
        "data": {
            "default_history_days": 60
        }
    },
    "conservative": {
        "performance": {
            "max_workers": 1,
            "batch_size": 10,
            "request_delay": 0.5,
            "batch_delay": 5.0
        },
        "data": {
            "min_data_quality": 0.9
        }
    }
}

def load_preset_config(preset_name: str):
    """加载预设配置"""
    if preset_name in PRESET_CONFIGS:
        preset = PRESET_CONFIGS[preset_name]
        
        # 应用预设配置
        for section, values in preset.items():
            if hasattr(config, section):
                section_obj = getattr(config, section)
                for key, value in values.items():
                    if hasattr(section_obj, key):
                        setattr(section_obj, key, value)
        
        print(f"✅ 已加载预设配置: {preset_name}")
        config.print_config()
    else:
        print(f"❌ 未找到预设配置: {preset_name}")
        print(f"可用预设: {list(PRESET_CONFIGS.keys())}")

if __name__ == "__main__":
    # 配置测试
    print("🧪 配置管理器测试")
    config.print_config()
    
    print("\n🔧 测试预设配置:")
    for preset in PRESET_CONFIGS.keys():
        print(f"- {preset}")
    
    # 保存示例配置
    config.save_config("example_config.json")
    print("\n💾 示例配置已保存到 example_config.json")
