import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import akshare as ak

class StockDatabase:
    def __init__(self, db_path="stock_data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建股票基本信息表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_info (
                code TEXT PRIMARY KEY,
                name TEXT,
                market TEXT,
                industry TEXT,
                area TEXT,
                pe REAL,
                outstanding REAL,
                totals REAL,
                totalAssets REAL,
                liquidAssets REAL,
                fixedAssets REAL,
                reserved REAL,
                reservedPerShare REAL,
                esp REAL,
                bvps REAL,
                pb REAL,
                timeToMarket TEXT,
                undp REAL,
                perundp REAL,
                rev REAL,
                profit REAL,
                gpr REAL,
                npr REAL,
                holders INTEGER,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建日线数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS daily_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT,
                date DATE,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                volume INTEGER,
                amount REAL,
                amplitude REAL,
                change_pct REAL,
                change_amount REAL,
                turnover REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(code, date)
            )
        ''')
        
        # 创建实时数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS realtime_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT,
                name TEXT,
                price REAL,
                change_pct REAL,
                change_amount REAL,
                volume INTEGER,
                amount REAL,
                bid REAL,
                ask REAL,
                high REAL,
                low REAL,
                open REAL,
                pre_close REAL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(code, timestamp)
            )
        ''')
        
        # 创建技术指标表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS technical_indicators (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT,
                date DATE,
                ma5 REAL,
                ma10 REAL,
                ma20 REAL,
                ma60 REAL,
                rsi REAL,
                macd REAL,
                macd_signal REAL,
                macd_hist REAL,
                boll_upper REAL,
                boll_mid REAL,
                boll_lower REAL,
                volume_ma REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(code, date)
            )
        ''')
        
        # 创建监控信号表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT,
                signal_type TEXT,
                signal_strength INTEGER,
                price REAL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建扫描进度表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS scan_progress (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                scan_id TEXT UNIQUE,
                total_stocks INTEGER,
                processed_stocks INTEGER,
                successful_stocks INTEGER,
                failed_stocks INTEGER,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                status TEXT DEFAULT 'running',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建扫描错误日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS scan_errors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                scan_id TEXT,
                code TEXT,
                error_message TEXT,
                retry_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建扫描结果表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS scan_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                scan_id TEXT,
                code TEXT,
                name TEXT,
                price REAL,
                change_pct REAL,
                volume_ratio REAL,
                turnover REAL,
                macd REAL,
                rsi REAL,
                score INTEGER,
                strategy_conditions TEXT,
                scan_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建数据更新状态表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_update_status (
                code TEXT PRIMARY KEY,
                last_update_date DATE,
                data_quality_score REAL,
                update_count INTEGER DEFAULT 0,
                last_error TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()
        print("数据库初始化完成")