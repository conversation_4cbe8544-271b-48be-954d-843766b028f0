import akshare as ak
import pandas as pd
import numpy as np
import sqlite3
import time
import json
import uuid
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from typing import List, Dict, Optional, Tuple
import logging

class OptimizedStockScreener:
    """基于数据库的高性能股票扫描器"""
    
    def __init__(self, db_path="stock_data.db", max_workers=3, batch_size=50):
        self.db_path = db_path
        self.max_workers = max_workers
        self.batch_size = batch_size
        self.lock = threading.Lock()
        
        # 设置日志
        logging.basicConfig(level=logging.INFO, 
                          format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库
        self._init_database()
        
    def _init_database(self):
        """初始化数据库"""
        from database_setup import StockDatabase
        db = StockDatabase(self.db_path)
        self.logger.info("数据库初始化完成")
    
    def collect_all_stock_data(self, days=60, force_update=False):
        """预收集所有股票数据到数据库"""
        self.logger.info("开始收集股票数据...")
        
        # 获取股票列表
        try:
            stock_info = ak.stock_info_a_code_name()
            # 过滤ST股票
            stock_info = stock_info[~stock_info['name'].str.contains('ST|退', na=False)]
            stock_codes = stock_info['code'].tolist()
            self.logger.info(f"获取到 {len(stock_codes)} 只股票")
        except Exception as e:
            self.logger.error(f"获取股票列表失败: {e}")
            return False
        
        # 检查哪些股票需要更新数据
        stocks_to_update = self._get_stocks_need_update(stock_codes, days, force_update)
        self.logger.info(f"需要更新数据的股票: {len(stocks_to_update)} 只")
        
        if not stocks_to_update:
            self.logger.info("所有股票数据都是最新的")
            return True
        
        # 分批处理
        success_count = 0
        total_batches = (len(stocks_to_update) + self.batch_size - 1) // self.batch_size
        
        for i in range(0, len(stocks_to_update), self.batch_size):
            batch = stocks_to_update[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            
            self.logger.info(f"处理批次 {batch_num}/{total_batches}, 股票数: {len(batch)}")
            
            # 并行收集数据
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = {executor.submit(self._collect_single_stock_data, code, days): code 
                          for code in batch}
                
                for future in as_completed(futures):
                    code = futures[future]
                    try:
                        if future.result():
                            success_count += 1
                    except Exception as e:
                        self.logger.error(f"收集股票 {code} 数据失败: {e}")
                    
                    # 控制请求频率
                    time.sleep(0.1)
            
            self.logger.info(f"批次 {batch_num} 完成，成功: {success_count}/{i + len(batch)}")
            
            # 批次间休息
            if batch_num < total_batches:
                time.sleep(2)
        
        self.logger.info(f"数据收集完成，成功率: {success_count}/{len(stocks_to_update)}")
        return True
    
    def _get_stocks_need_update(self, stock_codes: List[str], days: int, force_update: bool) -> List[str]:
        """获取需要更新数据的股票列表"""
        if force_update:
            return stock_codes
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 检查数据更新状态
        cutoff_date = (datetime.now() - timedelta(days=1)).date()
        
        cursor.execute('''
            SELECT code FROM data_update_status 
            WHERE last_update_date >= ? AND data_quality_score > 0.8
        ''', (cutoff_date,))
        
        updated_codes = set(row[0] for row in cursor.fetchall())
        conn.close()
        
        # 返回需要更新的股票
        return [code for code in stock_codes if code not in updated_codes]
    
    def _collect_single_stock_data(self, code: str, days: int) -> bool:
        """收集单只股票数据"""
        try:
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
            
            # 获取历史数据
            df = ak.stock_zh_a_hist(symbol=code, start_date=start_date, end_date=end_date)
            if df.empty:
                return False
            
            # 数据清洗和重命名
            df = df.rename(columns={
                '日期': 'date', '开盘': 'open', '收盘': 'close',
                '最高': 'high', '最低': 'low', '成交量': 'volume',
                '成交额': 'amount', '涨跌幅': 'change_pct', '换手率': 'turnover'
            })
            
            df['code'] = code
            df['date'] = pd.to_datetime(df['date']).dt.date
            
            # 存储到数据库
            conn = sqlite3.connect(self.db_path)
            
            # 删除旧数据
            cursor = conn.cursor()
            cursor.execute('DELETE FROM daily_data WHERE code = ? AND date >= ?', 
                          (code, start_date[:4] + '-' + start_date[4:6] + '-' + start_date[6:]))
            
            # 插入新数据
            df.to_sql('daily_data', conn, if_exists='append', index=False)
            
            # 计算并存储技术指标
            self._calculate_and_store_indicators(conn, code, df)
            
            # 更新数据状态
            quality_score = min(1.0, len(df) / days)  # 数据完整性评分
            cursor.execute('''
                INSERT OR REPLACE INTO data_update_status 
                (code, last_update_date, data_quality_score, update_count, updated_at)
                VALUES (?, ?, ?, COALESCE((SELECT update_count FROM data_update_status WHERE code = ?), 0) + 1, ?)
            ''', (code, datetime.now().date(), quality_score, code, datetime.now()))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            self.logger.error(f"收集股票 {code} 数据失败: {e}")
            
            # 记录错误
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO data_update_status 
                (code, last_update_date, data_quality_score, last_error, updated_at)
                VALUES (?, ?, 0, ?, ?)
            ''', (code, datetime.now().date(), str(e), datetime.now()))
            conn.commit()
            conn.close()
            
            return False
    
    def _calculate_and_store_indicators(self, conn, code: str, df: pd.DataFrame):
        """计算并存储技术指标"""
        if len(df) < 30:
            return
        
        close = df['close']
        volume = df['volume']
        
        # 计算技术指标
        indicators = self._calculate_technical_indicators(df)
        
        # 准备指标数据
        indicator_data = []
        for i, row in df.iterrows():
            if i >= 25:  # 确保有足够数据计算指标
                indicator_data.append({
                    'code': code,
                    'date': row['date'],
                    'ma5': indicators['ma5'].iloc[i] if not pd.isna(indicators['ma5'].iloc[i]) else None,
                    'ma10': indicators['ma10'].iloc[i] if not pd.isna(indicators['ma10'].iloc[i]) else None,
                    'ma20': indicators['ma20'].iloc[i] if not pd.isna(indicators['ma20'].iloc[i]) else None,
                    'ma60': indicators['ma60'].iloc[i] if not pd.isna(indicators['ma60'].iloc[i]) else None,
                    'rsi': indicators['rsi'].iloc[i] if not pd.isna(indicators['rsi'].iloc[i]) else None,
                    'macd': indicators['macd'].iloc[i] if not pd.isna(indicators['macd'].iloc[i]) else None,
                    'macd_signal': indicators['macd_signal'].iloc[i] if not pd.isna(indicators['macd_signal'].iloc[i]) else None,
                    'macd_hist': indicators['macd_hist'].iloc[i] if not pd.isna(indicators['macd_hist'].iloc[i]) else None,
                    'boll_upper': indicators['boll_upper'].iloc[i] if not pd.isna(indicators['boll_upper'].iloc[i]) else None,
                    'boll_mid': indicators['boll_mid'].iloc[i] if not pd.isna(indicators['boll_mid'].iloc[i]) else None,
                    'boll_lower': indicators['boll_lower'].iloc[i] if not pd.isna(indicators['boll_lower'].iloc[i]) else None,
                    'volume_ma': indicators['volume_ma'].iloc[i] if not pd.isna(indicators['volume_ma'].iloc[i]) else None
                })
        
        # 删除旧指标数据
        cursor = conn.cursor()
        cursor.execute('DELETE FROM technical_indicators WHERE code = ?', (code,))
        
        # 插入新指标数据
        if indicator_data:
            indicator_df = pd.DataFrame(indicator_data)
            indicator_df.to_sql('technical_indicators', conn, if_exists='append', index=False)
    
    def _calculate_technical_indicators(self, df: pd.DataFrame) -> Dict:
        """计算技术指标"""
        close = df['close']
        volume = df['volume']
        
        # 移动平均线
        ma5 = close.rolling(window=5).mean()
        ma10 = close.rolling(window=10).mean()
        ma20 = close.rolling(window=20).mean()
        ma60 = close.rolling(window=60).mean()
        
        # MACD
        exp1 = close.ewm(span=12).mean()
        exp2 = close.ewm(span=26).mean()
        macd = exp1 - exp2
        macd_signal = macd.ewm(span=9).mean()
        macd_hist = macd - macd_signal
        
        # 布林带
        sma20 = close.rolling(window=20).mean()
        std20 = close.rolling(window=20).std()
        boll_upper = sma20 + (std20 * 2)
        boll_lower = sma20 - (std20 * 2)
        
        # RSI
        delta = close.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # 成交量均线
        volume_ma = volume.rolling(window=20).mean()
        
        return {
            'ma5': ma5, 'ma10': ma10, 'ma20': ma20, 'ma60': ma60,
            'macd': macd, 'macd_signal': macd_signal, 'macd_hist': macd_hist,
            'boll_upper': boll_upper, 'boll_mid': sma20, 'boll_lower': boll_lower,
            'rsi': rsi, 'volume_ma': volume_ma
        }

    def run_database_screening(self, strategy_name="MACDBollVolumeStrategy",
                              resume_scan_id=None, max_stocks=None) -> str:
        """基于数据库运行股票筛选"""

        # 创建或恢复扫描任务
        scan_id = resume_scan_id or str(uuid.uuid4())

        if resume_scan_id:
            self.logger.info(f"恢复扫描任务: {scan_id}")
            processed_codes = self._get_processed_stocks(scan_id)
        else:
            self.logger.info(f"开始新的扫描任务: {scan_id}")
            processed_codes = set()

        # 获取需要扫描的股票列表
        stock_codes = self._get_available_stocks(max_stocks)
        remaining_codes = [code for code in stock_codes if code not in processed_codes]

        self.logger.info(f"总股票数: {len(stock_codes)}, 剩余待处理: {len(remaining_codes)}")

        # 初始化或更新扫描进度
        self._init_scan_progress(scan_id, len(stock_codes), len(processed_codes))

        # 分批处理
        selected_stocks = []
        batch_count = 0

        for i in range(0, len(remaining_codes), self.batch_size):
            batch = remaining_codes[i:i + self.batch_size]
            batch_count += 1

            self.logger.info(f"处理批次 {batch_count}, 股票: {len(batch)} 只")

            # 并行筛选
            batch_results = self._screen_batch_from_database(batch, strategy_name)
            selected_stocks.extend(batch_results)

            # 更新进度
            self._update_scan_progress(scan_id, len(batch), len(batch_results))

            # 保存批次结果
            if batch_results:
                self._save_batch_results(scan_id, batch_results)

            self.logger.info(f"批次 {batch_count} 完成，发现 {len(batch_results)} 只符合条件的股票")

            # 批次间休息
            time.sleep(1)

        # 完成扫描
        self._complete_scan(scan_id)

        self.logger.info(f"扫描完成，共发现 {len(selected_stocks)} 只符合条件的股票")
        return scan_id

    def _get_available_stocks(self, max_stocks=None) -> List[str]:
        """获取可用的股票列表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 获取有数据的股票
        query = '''
            SELECT DISTINCT code FROM data_update_status
            WHERE data_quality_score > 0.5
            ORDER BY data_quality_score DESC, update_count DESC
        '''

        if max_stocks:
            query += f' LIMIT {max_stocks}'

        cursor.execute(query)
        stock_codes = [row[0] for row in cursor.fetchall()]
        conn.close()

        return stock_codes

    def _get_processed_stocks(self, scan_id: str) -> set:
        """获取已处理的股票列表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT DISTINCT code FROM scan_results WHERE scan_id = ?', (scan_id,))
        processed = set(row[0] for row in cursor.fetchall())

        cursor.execute('SELECT DISTINCT code FROM scan_errors WHERE scan_id = ?', (scan_id,))
        processed.update(row[0] for row in cursor.fetchall())

        conn.close()
        return processed

    def _init_scan_progress(self, scan_id: str, total_stocks: int, processed_stocks: int):
        """初始化扫描进度"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO scan_progress
            (scan_id, total_stocks, processed_stocks, successful_stocks, failed_stocks, start_time, status)
            VALUES (?, ?, ?, 0, 0, ?, 'running')
        ''', (scan_id, total_stocks, processed_stocks, datetime.now()))

        conn.commit()
        conn.close()

    def _update_scan_progress(self, scan_id: str, processed_count: int, success_count: int):
        """更新扫描进度"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE scan_progress
            SET processed_stocks = processed_stocks + ?,
                successful_stocks = successful_stocks + ?,
                failed_stocks = failed_stocks + ?
            WHERE scan_id = ?
        ''', (processed_count, success_count, processed_count - success_count, scan_id))

        conn.commit()
        conn.close()

    def _complete_scan(self, scan_id: str):
        """完成扫描"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE scan_progress
            SET end_time = ?, status = 'completed'
            WHERE scan_id = ?
        ''', (datetime.now(), scan_id))

        conn.commit()
        conn.close()

    def _screen_batch_from_database(self, stock_codes: List[str], strategy_name: str) -> List[Dict]:
        """从数据库筛选一批股票"""
        results = []

        for code in stock_codes:
            try:
                result = self._screen_single_stock_from_database(code, strategy_name)
                if result:
                    results.append(result)
            except Exception as e:
                self.logger.error(f"筛选股票 {code} 失败: {e}")
                self._log_scan_error(code, str(e))

        return results

    def _screen_single_stock_from_database(self, code: str, strategy_name: str) -> Optional[Dict]:
        """从数据库筛选单只股票"""
        conn = sqlite3.connect(self.db_path)

        try:
            # 获取最新的日线数据
            daily_query = '''
                SELECT * FROM daily_data
                WHERE code = ?
                ORDER BY date DESC
                LIMIT 60
            '''
            daily_df = pd.read_sql_query(daily_query, conn, params=(code,))

            if daily_df.empty or len(daily_df) < 30:
                return None

            # 获取技术指标数据
            indicator_query = '''
                SELECT * FROM technical_indicators
                WHERE code = ?
                ORDER BY date DESC
                LIMIT 60
            '''
            indicator_df = pd.read_sql_query(indicator_query, conn, params=(code,))

            if indicator_df.empty:
                return None

            # 应用策略
            if strategy_name == "MACDBollVolumeStrategy":
                return self._apply_macd_boll_volume_strategy(code, daily_df, indicator_df)

        except Exception as e:
            self.logger.error(f"从数据库筛选股票 {code} 失败: {e}")
            return None
        finally:
            conn.close()

        return None

    def _apply_macd_boll_volume_strategy(self, code: str, daily_df: pd.DataFrame,
                                       indicator_df: pd.DataFrame) -> Optional[Dict]:
        """应用MACDBollVolumeStrategy策略"""
        try:
            # 确保数据按日期排序
            daily_df = daily_df.sort_values('date').reset_index(drop=True)
            indicator_df = indicator_df.sort_values('date').reset_index(drop=True)

            if len(daily_df) < 20 or len(indicator_df) < 20:
                return None

            # 检查最近20天是否有MACD金叉
            has_golden_cross = False
            golden_cross_date = None

            for i in range(max(0, len(indicator_df)-20), len(indicator_df)):
                if i >= 1:
                    current_macd = indicator_df['macd'].iloc[i]
                    current_signal = indicator_df['macd_signal'].iloc[i]
                    prev_macd = indicator_df['macd'].iloc[i-1]
                    prev_signal = indicator_df['macd_signal'].iloc[i-1]

                    if (current_macd > current_signal and prev_macd <= prev_signal and
                        current_macd < 0):
                        has_golden_cross = True
                        golden_cross_date = indicator_df['date'].iloc[i]
                        break

            # 检查金叉后是否有大阳线+高成交量
            has_big_yang_volume = False
            big_yang_date = None

            if has_golden_cross:
                for i in range(max(0, len(daily_df)-15), len(daily_df)):
                    row = daily_df.iloc[i]
                    change_ratio = (row['close'] - row['open']) / row['open']

                    # 获取对应的成交量均线
                    indicator_row = indicator_df[indicator_df['date'] == row['date']]
                    if not indicator_row.empty:
                        volume_ma = indicator_row['volume_ma'].iloc[0]
                        volume_ratio = row['volume'] / volume_ma if volume_ma > 0 else 0

                        if change_ratio >= 0.03 and volume_ratio >= 2.0:
                            has_big_yang_volume = True
                            big_yang_date = row['date']
                            break

            # 当前状态检查
            latest_daily = daily_df.iloc[-1]
            latest_indicator = indicator_df.iloc[-1]

            current_price = latest_daily['close']
            current_macd = latest_indicator['macd']
            current_macd_signal = latest_indicator['macd_signal']
            current_boll_mid = latest_indicator['boll_mid']
            current_boll_upper = latest_indicator['boll_upper']

            # MACD向上趋势
            macd_uptrend = False
            if len(indicator_df) >= 3:
                macd_uptrend = (indicator_df['macd'].iloc[-1] > indicator_df['macd'].iloc[-2] >
                               indicator_df['macd'].iloc[-3])

            # 价格在布林带中轨与上轨之间
            price_in_range = (current_boll_mid <= current_price <= current_boll_upper)

            # MACD在信号线之上
            macd_above_signal = current_macd > current_macd_signal

            # 前7天内有股价位于上轨附近
            was_near_upper = self._check_price_near_upper_band(daily_df, indicator_df, 7)

            # 现在股价缩量下降至接近中轨
            now_near_mid_low_volume = self._check_price_near_mid_with_low_volume(daily_df, indicator_df)

            # 综合条件判断
            conditions = {
                'has_golden_cross': has_golden_cross,
                'has_big_yang_volume': has_big_yang_volume,
                'macd_uptrend': macd_uptrend,
                'price_in_range': price_in_range,
                'macd_above_signal': macd_above_signal,
                'was_near_upper': was_near_upper,
                'now_near_mid_low_volume': now_near_mid_low_volume,
                'golden_cross_date': str(golden_cross_date) if golden_cross_date else None,
                'big_yang_date': str(big_yang_date) if big_yang_date else None
            }

            # 策略匹配
            strategy_match = (has_golden_cross and has_big_yang_volume and
                            macd_uptrend and price_in_range and macd_above_signal and
                            was_near_upper and now_near_mid_low_volume)

            if strategy_match:
                # 计算评分
                score = self._calculate_stock_score_from_db(daily_df, indicator_df)

                # 获取股票名称
                stock_name = self._get_stock_name(code)

                return {
                    'code': code,
                    'name': stock_name,
                    'price': current_price,
                    'change_pct': latest_daily['change_pct'],
                    'volume_ratio': latest_daily['volume'] / latest_indicator['volume_ma'] if latest_indicator['volume_ma'] > 0 else 0,
                    'turnover': latest_daily['turnover'] if 'turnover' in latest_daily else 0,
                    'macd': current_macd,
                    'rsi': latest_indicator['rsi'] if 'rsi' in latest_indicator else 0,
                    'score': score,
                    'conditions': conditions
                }

        except Exception as e:
            self.logger.error(f"应用策略到股票 {code} 失败: {e}")

        return None

    def _check_price_near_upper_band(self, daily_df: pd.DataFrame,
                                   indicator_df: pd.DataFrame, lookback_days: int) -> bool:
        """检测前N天内是否有股价接近上轨"""
        if len(daily_df) < lookback_days:
            return False

        for i in range(max(0, len(daily_df)-lookback_days), len(daily_df)-1):
            daily_row = daily_df.iloc[i]
            indicator_row = indicator_df[indicator_df['date'] == daily_row['date']]

            if not indicator_row.empty:
                price = daily_row['close']
                upper = indicator_row['boll_upper'].iloc[0]
                mid = indicator_row['boll_mid'].iloc[0]

                if upper > 0 and mid > 0:
                    band_width = upper - mid
                    distance_to_upper = abs(price - upper)

                    if distance_to_upper <= band_width * 0.1:  # 在上轨的10%范围内
                        return True

        return False

    def _check_price_near_mid_with_low_volume(self, daily_df: pd.DataFrame,
                                            indicator_df: pd.DataFrame) -> bool:
        """检测当前股价是否缩量下降至接近中轨"""
        if len(daily_df) < 5:
            return False

        latest_daily = daily_df.iloc[-1]
        latest_indicator = indicator_df.iloc[-1]

        current_price = latest_daily['close']
        current_mid = latest_indicator['boll_mid']
        current_upper = latest_indicator['boll_upper']

        if current_mid <= 0 or current_upper <= 0:
            return False

        band_width = current_upper - current_mid
        distance_to_mid = abs(current_price - current_mid)
        is_near_mid = distance_to_mid <= band_width * 0.2

        # 检查是否缩量
        current_volume = latest_daily['volume']
        avg_volume_5d = daily_df['volume'].iloc[-5:].mean()
        is_low_volume = current_volume <= avg_volume_5d * 0.8

        # 检查是否下降趋势
        recent_high = daily_df['close'].iloc[-3:].max()
        is_declining = current_price < recent_high

        return is_near_mid and is_low_volume and is_declining

    def _calculate_stock_score_from_db(self, daily_df: pd.DataFrame,
                                     indicator_df: pd.DataFrame) -> int:
        """从数据库数据计算股票评分"""
        score = 0

        latest_daily = daily_df.iloc[-1]
        latest_indicator = indicator_df.iloc[-1]

        # 基础技术指标评分
        if latest_daily['change_pct'] > 0:
            score += 1
        if latest_indicator['macd'] > 0:
            score += 1
        if latest_indicator['ma5'] > latest_indicator['ma20']:
            score += 1
        if 30 < latest_indicator['rsi'] < 70:
            score += 1

        # 成交量评分
        if latest_indicator['volume_ma'] > 0:
            volume_ratio = latest_daily['volume'] / latest_indicator['volume_ma']
            if 0.5 <= volume_ratio <= 1.5:
                score += 1

        return score

    def _get_stock_name(self, code: str) -> str:
        """获取股票名称"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT name FROM stock_info WHERE code = ?', (code,))
        result = cursor.fetchone()
        conn.close()

        return result[0] if result else code

    def _log_scan_error(self, code: str, error_message: str, scan_id: str = None):
        """记录扫描错误"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO scan_errors (scan_id, code, error_message, created_at)
            VALUES (?, ?, ?, ?)
        ''', (scan_id, code, error_message, datetime.now()))

        conn.commit()
        conn.close()

    def _save_batch_results(self, scan_id: str, results: List[Dict]):
        """保存批次结果"""
        if not results:
            return

        conn = sqlite3.connect(self.db_path)

        for result in results:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO scan_results
                (scan_id, code, name, price, change_pct, volume_ratio, turnover,
                 macd, rsi, score, strategy_conditions, scan_date, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                scan_id, result['code'], result['name'], result['price'],
                result['change_pct'], result['volume_ratio'], result['turnover'],
                result['macd'], result['rsi'], result['score'],
                json.dumps(result['conditions'], ensure_ascii=False),
                datetime.now().date(), datetime.now()
            ))

        conn.commit()
        conn.close()

    def get_scan_results(self, scan_id: str = None, limit: int = None) -> List[Dict]:
        """获取扫描结果"""
        conn = sqlite3.connect(self.db_path)

        if scan_id:
            query = 'SELECT * FROM scan_results WHERE scan_id = ? ORDER BY score DESC, price DESC'
            params = (scan_id,)
        else:
            query = '''
                SELECT * FROM scan_results
                WHERE scan_date = (SELECT MAX(scan_date) FROM scan_results)
                ORDER BY score DESC, price DESC
            '''
            params = ()

        if limit:
            query += f' LIMIT {limit}'

        df = pd.read_sql_query(query, conn, params=params)
        conn.close()

        results = df.to_dict('records')

        # 解析策略条件
        for result in results:
            if result['strategy_conditions']:
                try:
                    result['conditions'] = json.loads(result['strategy_conditions'])
                except:
                    result['conditions'] = {}

        return results

    def get_scan_progress(self, scan_id: str = None) -> Dict:
        """获取扫描进度"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        if scan_id:
            cursor.execute('SELECT * FROM scan_progress WHERE scan_id = ?', (scan_id,))
        else:
            cursor.execute('SELECT * FROM scan_progress ORDER BY start_time DESC LIMIT 1')

        result = cursor.fetchone()
        conn.close()

        if result:
            columns = ['id', 'scan_id', 'total_stocks', 'processed_stocks',
                      'successful_stocks', 'failed_stocks', 'start_time',
                      'end_time', 'status', 'created_at']
            return dict(zip(columns, result))

        return {}

    def list_recent_scans(self, limit: int = 10) -> List[Dict]:
        """列出最近的扫描任务"""
        conn = sqlite3.connect(self.db_path)

        query = '''
            SELECT sp.*,
                   COUNT(sr.id) as result_count,
                   COUNT(se.id) as error_count
            FROM scan_progress sp
            LEFT JOIN scan_results sr ON sp.scan_id = sr.scan_id
            LEFT JOIN scan_errors se ON sp.scan_id = se.scan_id
            GROUP BY sp.scan_id
            ORDER BY sp.start_time DESC
            LIMIT ?
        '''

        df = pd.read_sql_query(query, conn, params=(limit,))
        conn.close()

        return df.to_dict('records')

    def export_results(self, scan_id: str = None, filename: str = None) -> str:
        """导出扫描结果"""
        results = self.get_scan_results(scan_id)

        if not results:
            self.logger.warning("没有找到扫描结果")
            return ""

        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            scan_suffix = f"_{scan_id[:8]}" if scan_id else ""
            filename = f"optimized_scan_results{scan_suffix}_{timestamp}.csv"

        # 准备导出数据
        export_data = []
        for result in results:
            export_row = {
                'code': result['code'],
                'name': result['name'],
                'price': result['price'],
                'change_pct': result['change_pct'],
                'volume_ratio': result['volume_ratio'],
                'turnover': result['turnover'],
                'macd': result['macd'],
                'rsi': result['rsi'],
                'score': result['score'],
                'scan_date': result['scan_date']
            }

            # 添加策略条件
            if 'conditions' in result:
                conditions = result['conditions']
                export_row.update({
                    'has_golden_cross': conditions.get('has_golden_cross', False),
                    'has_big_yang_volume': conditions.get('has_big_yang_volume', False),
                    'macd_uptrend': conditions.get('macd_uptrend', False),
                    'price_in_range': conditions.get('price_in_range', False),
                    'macd_above_signal': conditions.get('macd_above_signal', False),
                    'was_near_upper': conditions.get('was_near_upper', False),
                    'now_near_mid_low_volume': conditions.get('now_near_mid_low_volume', False),
                    'golden_cross_date': conditions.get('golden_cross_date', ''),
                    'big_yang_date': conditions.get('big_yang_date', '')
                })

            export_data.append(export_row)

        # 保存CSV
        df = pd.DataFrame(export_data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')

        self.logger.info(f"结果已导出到: {filename}")
        return filename

    def print_scan_summary(self, scan_id: str = None):
        """打印扫描摘要"""
        progress = self.get_scan_progress(scan_id)
        results = self.get_scan_results(scan_id, limit=20)

        if not progress:
            self.logger.warning("未找到扫描进度信息")
            return

        print(f"\n=== 扫描摘要 ===")
        print(f"扫描ID: {progress['scan_id']}")
        print(f"状态: {progress['status']}")
        print(f"开始时间: {progress['start_time']}")
        print(f"结束时间: {progress['end_time'] or '进行中'}")
        print(f"总股票数: {progress['total_stocks']}")
        print(f"已处理: {progress['processed_stocks']}")
        print(f"成功筛选: {progress['successful_stocks']}")
        print(f"失败数量: {progress['failed_stocks']}")

        if progress['total_stocks'] > 0:
            completion_rate = progress['processed_stocks'] / progress['total_stocks'] * 100
            success_rate = progress['successful_stocks'] / max(progress['processed_stocks'], 1) * 100
            print(f"完成率: {completion_rate:.1f}%")
            print(f"成功率: {success_rate:.1f}%")

        if results:
            print(f"\n=== 前20只高评分股票 ===")
            for i, stock in enumerate(results[:20], 1):
                print(f"{i:2d}. {stock['code']} {stock['name']:8s} - "
                      f"价格: {stock['price']:6.2f}, 涨跌幅: {stock['change_pct']:5.2f}%, "
                      f"评分: {stock['score']}, 成交量倍数: {stock['volume_ratio']:.2f}")

        print(f"\n提示：使用 export_results('{progress['scan_id']}') 导出完整结果")

    def cleanup_old_data(self, days_to_keep: int = 30):
        """清理旧数据"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 清理旧的扫描结果
        cursor.execute('DELETE FROM scan_results WHERE created_at < ?', (cutoff_date,))
        deleted_results = cursor.rowcount

        # 清理旧的扫描错误
        cursor.execute('DELETE FROM scan_errors WHERE created_at < ?', (cutoff_date,))
        deleted_errors = cursor.rowcount

        # 清理旧的扫描进度
        cursor.execute('DELETE FROM scan_progress WHERE created_at < ?', (cutoff_date,))
        deleted_progress = cursor.rowcount

        conn.commit()
        conn.close()

        self.logger.info(f"清理完成 - 结果: {deleted_results}, 错误: {deleted_errors}, 进度: {deleted_progress}")

def main():
    """主函数"""
    screener = OptimizedStockScreener(max_workers=3, batch_size=50)

    print("=== 优化版股票扫描器 ===")
    print("1. 数据收集模式 - 预收集股票数据到数据库")
    print("2. 快速扫描模式 - 基于数据库进行高速筛选")
    print("3. 恢复扫描模式 - 恢复中断的扫描任务")
    print("4. 查看扫描结果")
    print("5. 查看扫描历史")
    print("6. 导出结果")
    print("7. 清理旧数据")

    mode = input("\n请选择模式 (1-7): ").strip()

    if mode == "1":
        print("\n=== 数据收集模式 ===")
        days = int(input("收集多少天的历史数据 (默认60天): ") or "60")
        force_update = input("是否强制更新所有数据 (y/N): ").lower() == 'y'

        print("开始收集数据...")
        success = screener.collect_all_stock_data(days=days, force_update=force_update)

        if success:
            print("✓ 数据收集完成！现在可以使用快速扫描模式")
        else:
            print("✗ 数据收集失败")

    elif mode == "2":
        print("\n=== 快速扫描模式 ===")
        max_stocks = input("限制扫描股票数量 (回车表示全部): ").strip()
        max_stocks = int(max_stocks) if max_stocks else None

        print("开始扫描...")
        scan_id = screener.run_database_screening(max_stocks=max_stocks)

        print(f"✓ 扫描完成！扫描ID: {scan_id}")
        screener.print_scan_summary(scan_id)

    elif mode == "3":
        print("\n=== 恢复扫描模式 ===")
        recent_scans = screener.list_recent_scans(5)

        if not recent_scans:
            print("没有找到可恢复的扫描任务")
            return

        print("最近的扫描任务:")
        for i, scan in enumerate(recent_scans, 1):
            print(f"{i}. {scan['scan_id'][:8]}... - {scan['status']} - "
                  f"进度: {scan['processed_stocks']}/{scan['total_stocks']}")

        choice = input("选择要恢复的扫描 (输入序号): ").strip()
        if choice.isdigit() and 1 <= int(choice) <= len(recent_scans):
            scan_id = recent_scans[int(choice)-1]['scan_id']
            print(f"恢复扫描: {scan_id}")

            new_scan_id = screener.run_database_screening(resume_scan_id=scan_id)
            screener.print_scan_summary(new_scan_id)

    elif mode == "4":
        print("\n=== 查看扫描结果 ===")
        scan_id = input("输入扫描ID (回车查看最新结果): ").strip() or None
        screener.print_scan_summary(scan_id)

    elif mode == "5":
        print("\n=== 扫描历史 ===")
        recent_scans = screener.list_recent_scans(10)

        for scan in recent_scans:
            print(f"ID: {scan['scan_id'][:8]}... - {scan['status']} - "
                  f"时间: {scan['start_time']} - "
                  f"结果: {scan['result_count']} - "
                  f"错误: {scan['error_count']}")

    elif mode == "6":
        print("\n=== 导出结果 ===")
        scan_id = input("输入扫描ID (回车导出最新结果): ").strip() or None
        filename = screener.export_results(scan_id)

        if filename:
            print(f"✓ 结果已导出到: {filename}")

    elif mode == "7":
        print("\n=== 清理旧数据 ===")
        days = int(input("保留多少天的数据 (默认30天): ") or "30")
        screener.cleanup_old_data(days)
        print("✓ 清理完成")

    else:
        print("无效的选择")

if __name__ == "__main__":
    main()
