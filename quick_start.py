#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票扫描器快速启动脚本
解决运行时间过长和数据失败问题
"""

import sys
import os
from datetime import datetime
from optimized_stock_screener import OptimizedStockScreener

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🚀 优化版股票扫描器 - 快速启动")
    print("=" * 60)
    print("✅ 解决运行时间过长问题")
    print("✅ 解决500+股票后数据失败问题") 
    print("✅ 支持断点续传和错误恢复")
    print("✅ 基于数据库的高速筛选")
    print("=" * 60)

def check_database_status(screener):
    """检查数据库状态"""
    print("\n📊 检查数据库状态...")
    
    import sqlite3
    conn = sqlite3.connect(screener.db_path)
    cursor = conn.cursor()
    
    # 检查股票数据
    cursor.execute("SELECT COUNT(*) FROM daily_data")
    daily_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(DISTINCT code) FROM daily_data")
    stock_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM technical_indicators")
    indicator_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM data_update_status WHERE data_quality_score > 0.5")
    quality_count = cursor.fetchone()[0]
    
    conn.close()
    
    print(f"📈 日线数据记录: {daily_count:,}")
    print(f"📊 股票数量: {stock_count:,}")
    print(f"🔢 技术指标记录: {indicator_count:,}")
    print(f"✅ 高质量数据股票: {quality_count:,}")
    
    return stock_count > 0, quality_count > 100

def quick_data_collection(screener, test_mode=False):
    """快速数据收集"""
    print("\n📥 开始数据收集...")
    
    if test_mode:
        print("🧪 测试模式：收集前100只股票的数据")
        # 临时修改批量大小进行测试
        original_batch_size = screener.batch_size
        screener.batch_size = 20
        
        # 收集测试数据
        success = screener.collect_all_stock_data(days=30, force_update=False)
        
        # 恢复原始设置
        screener.batch_size = original_batch_size
    else:
        print("🔄 生产模式：收集所有股票数据")
        success = screener.collect_all_stock_data(days=60, force_update=False)
    
    if success:
        print("✅ 数据收集完成！")
        return True
    else:
        print("❌ 数据收集失败，请检查网络连接")
        return False

def quick_screening(screener, test_mode=False):
    """快速筛选"""
    print("\n🔍 开始股票筛选...")
    
    max_stocks = 200 if test_mode else None
    mode_text = "测试模式" if test_mode else "全市场扫描"
    
    print(f"📊 {mode_text}：筛选股票...")
    
    start_time = datetime.now()
    scan_id = screener.run_database_screening(max_stocks=max_stocks)
    end_time = datetime.now()
    
    duration = (end_time - start_time).total_seconds()
    print(f"⏱️  扫描耗时: {duration:.1f} 秒")
    
    return scan_id

def show_results(screener, scan_id):
    """显示结果"""
    print("\n📋 扫描结果摘要:")
    screener.print_scan_summary(scan_id)
    
    # 自动导出结果
    filename = screener.export_results(scan_id)
    if filename:
        print(f"\n💾 结果已自动导出到: {filename}")

def main():
    """主函数"""
    print_banner()
    
    # 初始化扫描器
    print("\n🔧 初始化扫描器...")
    screener = OptimizedStockScreener(max_workers=3, batch_size=50)
    
    # 检查数据库状态
    has_data, has_quality_data = check_database_status(screener)
    
    # 根据数据状态决定运行模式
    if not has_data:
        print("\n❗ 数据库为空，需要先收集数据")
        print("\n选择运行模式:")
        print("1. 🧪 测试模式 (快速体验，约5-10分钟)")
        print("2. 🚀 生产模式 (完整数据，约2-3小时)")
        
        choice = input("\n请选择 (1/2): ").strip()
        test_mode = (choice == "1")
        
        # 数据收集
        if not quick_data_collection(screener, test_mode):
            print("\n❌ 数据收集失败，程序退出")
            return
        
        # 重新检查数据状态
        has_data, has_quality_data = check_database_status(screener)
    
    if not has_quality_data:
        print("\n⚠️  数据质量不足，建议重新收集数据")
        choice = input("是否继续筛选? (y/N): ").strip().lower()
        if choice != 'y':
            return
    
    # 选择筛选模式
    print("\n选择筛选模式:")
    print("1. 🧪 测试筛选 (200只股票)")
    print("2. 🚀 全市场筛选 (所有股票)")
    print("3. 📊 查看历史结果")
    print("4. 🔄 恢复中断的扫描")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        # 测试筛选
        scan_id = quick_screening(screener, test_mode=True)
        show_results(screener, scan_id)
        
    elif choice == "2":
        # 全市场筛选
        print("\n⚠️  全市场筛选可能需要10-15分钟")
        confirm = input("确认继续? (y/N): ").strip().lower()
        if confirm == 'y':
            scan_id = quick_screening(screener, test_mode=False)
            show_results(screener, scan_id)
        
    elif choice == "3":
        # 查看历史结果
        print("\n📚 最近的扫描历史:")
        recent_scans = screener.list_recent_scans(5)
        
        if not recent_scans:
            print("❌ 没有找到历史扫描记录")
            return
        
        for i, scan in enumerate(recent_scans, 1):
            status_emoji = "✅" if scan['status'] == 'completed' else "🔄"
            print(f"{i}. {status_emoji} {scan['scan_id'][:8]}... - {scan['status']} - "
                  f"结果: {scan['result_count']} 只股票")
        
        choice = input("\n选择查看详情 (输入序号): ").strip()
        if choice.isdigit() and 1 <= int(choice) <= len(recent_scans):
            scan_id = recent_scans[int(choice)-1]['scan_id']
            show_results(screener, scan_id)
        
    elif choice == "4":
        # 恢复扫描
        print("\n🔄 可恢复的扫描任务:")
        recent_scans = screener.list_recent_scans(5)
        incomplete_scans = [s for s in recent_scans if s['status'] != 'completed']
        
        if not incomplete_scans:
            print("❌ 没有找到可恢复的扫描任务")
            return
        
        for i, scan in enumerate(incomplete_scans, 1):
            progress = scan['processed_stocks'] / max(scan['total_stocks'], 1) * 100
            print(f"{i}. 🔄 {scan['scan_id'][:8]}... - 进度: {progress:.1f}% "
                  f"({scan['processed_stocks']}/{scan['total_stocks']})")
        
        choice = input("\n选择要恢复的扫描 (输入序号): ").strip()
        if choice.isdigit() and 1 <= int(choice) <= len(incomplete_scans):
            scan_id = incomplete_scans[int(choice)-1]['scan_id']
            print(f"\n🔄 恢复扫描: {scan_id[:8]}...")
            
            new_scan_id = screener.run_database_screening(resume_scan_id=scan_id)
            show_results(screener, new_scan_id)
    
    else:
        print("❌ 无效的选择")
        return
    
    print("\n🎉 程序执行完成！")
    print("\n💡 提示:")
    print("- 数据会自动缓存，下次运行更快")
    print("- 建议每日更新数据以获得最新结果")
    print("- 可以使用 optimized_stock_screener.py 进行高级操作")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断程序")
        print("💡 下次可以使用恢复功能继续未完成的扫描")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        print("💡 请检查网络连接和数据库文件权限")
